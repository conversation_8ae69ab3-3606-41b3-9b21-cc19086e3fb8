<template>
	<view class="item-box">
		<view class="head-image">
			<text v-if="!detail.imgHead" class="cuIcon-people" />
			<u-lazy-load v-else :image="$tools.showImg(detail.imgHead)" width="120" height="160" border-radius="4" />
		</view>
		<view class="content text-content">
			<view class="flex justify-between align-center">
				<view>
					<view class="flex justify-start align-center">
						<view class="text-df text-bold" >{{detail.name}}</view>
						<view class="text-sm text-blue margin-left-lg" v-if="detail.sex==1">男<text class="cuIcon-male" /></view>
						<view class="text-sm text-pink margin-left-lg" v-if="detail.sex==2">女<text class="cuIcon-female" /></view>
					</view>
					
					<view><text v-if="detail.phone" class="cuIcon-phone" />{{detail.phone}}</view>
				</view>
				<view class="text-sm text-gray select-btn">
					<slot name="op">
						<u-button type="primary" shape="circle" :color="colors" text="选择" :plain="false" size="small" @tap="$emit('select',detail)"/>
<!-- 						<view class="block-btn text-center" @tap="$emit('select',detail)">
							选择<text class="cuIcon-right margin-left-xs"></text>
						</view> -->
					</slot>
					
				</view>
			</view>
			
			<view class="text-xs text-gray address">{{detail.address}}</view>
			<view v-if="detail.attendantName">
				<text class="text-sm" :style="{color:colors}">({{detail.groupName}})</text>
				 <text class="text-gray">护理员:{{detail.attendantName}}</text>
			</view>
			<slot name="other"></slot>
		</view>
		<view v-if="tip" class="tip">{{tip}}</view>
		<view v-if="detail.distance" class="distance">{{detail.distance}} km</view>
	</view>
</template>

<script>
	export default {
		components: {
		},
		props:{
			colors:{
				type:String,
				default:'',
			},
			detail:{
				type:Object,
				default:()=>{},
			},
			tip:{
				type:String,
				default:'',
			}
		},
		data() {
			return {
			}
		},
		methods:{

		}
	}
</script>

<style scoped lang="scss">
	.item-box{
		padding: 20rpx 30rpx; 
		background-color: white; 
		margin-bottom: 10rpx;
		display: flex;
		position: relative;
		overflow: hidden;
		.content{
			flex: 1;
			padding: 0 20rpx;
		}
		
	}
	
	.block-btn {
		width: 190rpx;
		height: 60rpx;
		color:#fff;
		background: linear-gradient(90deg, var(--colors), var(--colors));
		border: 2rpx solid rgba(230, 184, 115, 0.3);
		border-radius: 80rpx;
		padding: 8rpx 20rpx;
		font-size: 28rpx;
		font-weight: 700;
	}
	
	.cuIcon-people{
		font-size: 116rpx;
		color: gray;
		border: 1rpx solid #ccc;
		width: 116rpx;
		height: 156rpx;
		line-height: 156rpx;
		border-radius: 6rpx;
		display: block;
	}
	
	.address{
		height: 36rpx;
		overflow: hidden; /* 隐藏溢出的内容 */
		text-overflow: ellipsis; /* 使用省略号表示溢出的文本 */
		-o-text-overflow: ellipsis;
		display: -webkit-box;
		-webkit-line-clamp: 1;
		-webkit-box-orient: vertical;
	}
	
	.select-btn{
		/deep/.u-button--small{
			height: 48rpx !important;
			letter-spacing: 2px;
		}
	}
	
	.tip{
		position: absolute;
		background-color: var(--colors);
		overflow: hidden;
		white-space:nowrap;
		color: #fff;
		transform: rotate(-45deg);
		box-shadow: 0 0 20rpx #888;
		min-width: 150rpx;
		text-align: center;
		left:-40rpx;
		top:20rpx;
		font-size: 22rpx;
		line-height: 1.6;
	}
	.distance{
		position: absolute;
		right: 20rpx;
		top:30rpx;
		font-size: 20rpx;
		color: #a0a0a0;
	}
</style>