<template>
    <ut-page class="page">
        <f-navbar fontColor="#fff" :bgColor="colors" title="服务项目校对" navbarType="2"></f-navbar>

        <view class="cu-card margin-sm radius shadow bg-white flex padding-sm">
            <view class="flex-sub-0 margin-right-sm">
                <u-lazy-load v-if="form.imgHead"
                    :image="$tools.showImg(form.imgHead)"
                    width="120"
                    height="160"
                    border-radius="4" />
            </view>
            <view class="flex-sub text-content">
                <view class="flex justify-between align-center">
                    <view>
                        <view class="flex justify-start align-center">
                            <view class="text-df text-bold text-black">{{ form.name }}</view>
                            <view class="text-sm text-blue margin-left-lg" v-if="form.sex == 1">男
                                <text class="cuIcon-male" />
                            </view>
                            <view class="text-sm text-pink margin-left-lg" v-if="form.sex == 2">女
                                <text class="cuIcon-female" />
                            </view>
                        </view>
                        <view class="text-sm text-gray margin-top-xs">
                            <text v-if="form.phone" class="cuIcon-phone margin-right-xs" />
                            {{ form.phone }}
                        </view>
                    </view>
                    <view class="text-right">
                        <view class="text-sm text-gray">护理日期</view>
                        <view class="text-df text-bold" :style="{ color: colors }">{{ form.workDate }}</view>
                    </view>
                </view>

                <view class="text-xs text-gray text-cut margin-top-xs">{{ form.address }}</view>
                <view v-if="form.attendantName" class="margin-top-xs">
                    <text class="text-sm" :style="{ color: colors }">({{ form.groupName }})</text>
                    <text class="text-sm text-gray">护理员：{{ form.attendantName }}</text>
                </view>

                <view v-if="form.idcard" class="flex align-center margin-top-xs">
                    <text class="text-xs text-gray margin-right-xs">证件号码：</text>
                    <text class="text-xs text-black">{{ form.idcard }}</text>
                </view>

                <view v-if="form.isManual" class="margin-top-xs">
                    <text class="text-sm text-gray">上次校对结果：</text>
                    <text v-if="form.dataError && form.projectError" class="text-xs cu-tag bg-orange light radius">
                        服务单与医保不一致、提交项目与医保不一致
                    </text>
                    <text v-else-if="form.dataError" class="text-xs cu-tag bg-orange light radius">
                        服务单与医保不一致
                    </text>
                    <text v-else-if="form.projectError" class="text-xs cu-tag bg-orange light radius">
                        提交项目与医保不一致
                    </text>
                </view>
            </view>
        </view>

        <view v-if="joinedDatas" class="cu-card margin-sm radius shadow s-gray bg-white">
            <file-data-item :detail="joinedDatas" :colors="colors" :only-view="true"></file-data-item>
        </view>

        <view class="cu-card margin-sm padding-sm radius shadow bg-white">
            <view class="text-center text-black margin-bottom-sm">服务单项目与医保截图是否一致：
                <text class="text-red">*</text>
            </view>
            <u-radio-group v-model="form.dataError" placement="row" style="display: flex; justify-content: center">
                <u-radio :name="false"
                    :activeColor="colors"
                    label="一致"
                    :customStyle="{ marginRight: '60rpx', padding: '20rpx' }"></u-radio>
                <u-radio :name="true"
                    :activeColor="colors"
                    label="不一致"
                    :customStyle="{ padding: '20rpx' }"></u-radio>
            </u-radio-group>
        </view>

        <view class="cu-card margin-sm radius padding-sm shadow bg-white">
            <view class="flex justify-between align-center margin-bottom-sm">
                <u-button
                    type="error"
                    text="重置"
                    :plain="true"
                    size="small"
                    @click="resetProjects"
                    :custom-style="{ marginRight: '20rpx' }"
                />
                <u-button
                    type="primary"
                    :color="colors"
                    text="添加项目"
                    :plain="true"
                    size="small"
                    @click="addProject"
                />
            </view>
            <u-swipe-action ref="swipeProjectList">
                <u-swipe-action-item v-for="item in orderProjects" :key="item.id"
                    :name="item.projectId"
                    :options="getProjectActionOptions(item)"
                    @click="projectActionClick">
                    <view class="padding-tb-sm solid-bottom">
                        <project-item :detail="item" :colors="colors"></project-item>
                    </view>
                </u-swipe-action-item>
            </u-swipe-action>
            <view v-if="!form.projects || !form.projects.length" class="text-center padding-xl">
                <text class="text-gray">无服务项目</text>
            </view>
        </view>

        <view class="cu-bar bg-white tabbar foot" style="padding: 0 10rpx 0 20rpx;">
            <view class="flex-sub">
                <view v-if="form.suplus > 0" class="text-sm text-gray">
                    剩余
                    <text class="text-red text-bold padding-lr-xs">{{ form.suplus }}</text>
                    份未校对
                </view>
                <view v-else class="text-sm text-gray">暂无剩余校对任务</view>
            </view>
            <view class="action">
                <u-button
                    type="primary"
                    :color="colors"
                    :loading="loading"
                    @click="save"
                    text="保存"
                    size="large"
                    shape="circle"
                    :custom-style="{ minWidth: '200rpx', height: '80rpx' }"
                ></u-button>
            </view>
        </view>

        <u-popup
            :show="showProjectPopup"
            mode="bottom"
            round="10"
            :closeable="true"
            :safe-area-inset-bottom="false"
            :mask-close-able="true"
            close-icon-pos="top-left"
            :z-index="1025"
            :overlay-style="{zIndex:998}"
            @close="showProjectPopup=false"
        >
            <view class="text-center padding-tb text-df text-bold text-black">项目选择</view>
            <project-selector
                :project-data="projectData"
                :selected-projects="form.projects"
                @selectProject="selectProject"
            />
        </u-popup>
    </ut-page>
</template>

<script>
import { mapState } from 'vuex'
import ProjectItem from '@/pagesA/components/project-item.vue'
import FileDataItem from '@/pagesA/components/file-data-item.vue'
import ProjectSelector from './project-selector.vue'

const app = getApp()
export default {
    components: {
        ProjectItem,
        FileDataItem,
        ProjectSelector,
    },
    options: {
        styleIsolation: 'shared',
    },
    data() {
        return {
            colors: '',
            loading: false,
            workId: '',
            month: null,
            form: {
                id: '',
                workDate: '',
                groupName: '',
                attendantId: '',
                attendantName: '',
                customerId: '',
                name: '',
                sex: 0,
                phone: '',
                idcard: '',
                address: '',
                isManual: false,
                manualTime: '',
                manualUserName: '',
                dataError: null,
                projectError: false,
                projects: [],
                datas: [],
                suplus: 0,
            },
            showProjectPopup: false,
            projectData: [],
            initialProjects: [],
        }
    },
    computed: {
        ...mapState({
            commKey: state => state.init.template.commKey,
            token: state => state.user.token,
            userInfo: state => state.user.info,
            community: state => state.init.community,
        }),
        orderProjects() {
            return this.form.projects?.toSorted((a, b) => a.govCode - b.govCode) ?? []
        },
        joinedDatas() {
            if (!this.form.datas?.length) return null
            const datas = this.form.datas
            const titles = []
            const outputDetail = {
                title: '',
                files: [],
            }
            for (const d of datas) {
                titles.push(d.title)
                for (const details of d.details) {
                    outputDetail.files.push(...details.files)
                }
            }
            return {
                createTime: '',
                dataTypeId: '',
                title: titles.join('、'),
                details: [outputDetail],
            }
        },
    },
    onLoad(options) {
        this.workId = options.workId ?? ''
        this.month = options.month
        this.loadTask()
    },
    onShow() {
        this.setData({ colors: app.globalData.newColor })
    },
    methods: {
        async loadTask() {
            if (!this.workId) return
            this.loading = true
            const { data } = await this.$ut.api('mang/care/proofread/task', {
                communityId: this.community.id,
                workId: this.workId,
                month: this.month,
            }).finally(() => this.loading = false)
            this.updateFormData(data)
        },
        updateFormData(data) {
            this.form = {
                ...this.form,
                ...data,
            }
            if (!data.isManual) {
                this.form.dataError = null
            }
            if (data.projects && data.projects.length > 0) {
                this.initialProjects = JSON.parse(JSON.stringify(data.projects))
            }
        },
        async save() {
            if (this.form.dataError === null) {
                uni.showToast({
                    title: '请选择服务单项目与医保截图是否一致',
                    icon: 'none',
                })
                return
            }
            this.loading = true
            const { data } = await this.$ut.api('mang/care/proofread/save', {
                communityId: this.community.id,
                workId: this.form.id,
                dataError: this.form.dataError,
                projectIds: this.form.projects ? this.form.projects.map(p => p.projectId) : [],
            }).finally(() => this.loading = false)
            uni.showToast({
                title: '保存成功',
                icon: 'success',
            })
            if (data && data.id) {
                this.updateFormData(data)
            } else {
                setTimeout(() => {
                    this.goBack()
                }, 1500)
            }
        },
        resetProjects() {
            uni.showModal({
                title: '确认重置',
                content: '确定要将服务项目重置为初始状态吗？',
                success: (res) => {
                    if (res.confirm) {
                        if (this.initialProjects?.length > 0) {
                            this.form.projects = JSON.parse(JSON.stringify(this.initialProjects))
                        } else {
                            this.form.projects = []
                        }
                    }
                },
            })
        },
        addProject() {
            this.loadProjectData()
            this.showProjectPopup = true
        },
        async loadProjectData() {
            const { data } = await this.$ut.api('mang/care/work/project/allRuleListpg', {
                communityId: this.community.id,
                workId: this.form.id,
            })
            this.projectData = data?.info || []
        },
        getProjectActionOptions() {
            const btnDelete = {
                text: '删除',
                code: 'delete',
                style: {
                    backgroundColor: '#f56c6c',
                },
            }
            return [btnDelete]
        },
        projectActionClick(data) {
            if (data.code === 'delete') {
                this.removeProject(data.name)
            }
            this.$refs['swipeProjectList'].closeAll()
        },
        removeProject(projectId) {
            if (this.form.projects && this.form.projects.length) {
                const index = this.form.projects.findIndex(p => p.projectId === projectId)
                if (index >= 0) {
                    this.form.projects.splice(index, 1)
                }
            }
        },
        selectProject(projects) {
            this.showProjectPopup = false
            if (!this.form.projects) {
                this.$set(this.form, 'projects', [])
            }
            if (!projects || !projects.length) return
            projects.forEach(project => {
                const existingProject = this.form.projects.find(p => p.projectId === project.id)
                if (!existingProject) {
                    this.form.projects.push({
                        id: project.id,
                        projectId: project.id,
                        projectName: project.name,
                        projectCode: project.code,
                        govCode: project.govCode,
                        requireMinDuration: project.minDuration,
                        requireMaxDuration: project.maxDuration,
                        remark: project.remark || '',
                    })
                }
            })
        },
        goBack() {
            uni.navigateBack()
        },
    },
}
</script>

<style lang="scss" scoped>
.page {
  padding-bottom: 128rpx;
}

.cuIcon-people {
  font-size: 116rpx;
  color: gray;
  border: 1rpx solid #ccc;
  width: 116rpx;
  height: 156rpx;
  line-height: 156rpx;
  border-radius: 6rpx;
  display: block;
}

:deep(.u-radio) {
  min-height: 88rpx;
  display: flex;
  align-items: center;
}
</style>
