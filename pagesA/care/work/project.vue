<template>
  <ut-page>
    <ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
      <f-navbar fontColor="#fff" :bgColor="colors" title="本次服务项目" navbarType="1" />
    </ut-top>
    <mescroll-body
      ref="mescrollRef"
      :top="topWrapHeight + 'px'"
      :top-margin="-topWrapHeight + 'px'"
      bottom="140"
      :up="upOption"
      :safearea="true"
      @init="mescrollInit"
      @down="downCallback"
      @up="upCallback"
      @emptyclick="emptyClick">
      <u-swipe-action ref="swipeUserList">
        <u-swipe-action-item
          v-for="(item, index) in dataList"
          :key="index"
          :name="item.id"
          :options="getActionOption(item)"
          @longpress="longpress(item)"
          @click="actionOp">
          <view class="padding-lr padding-tb-sm">
            <project-item :detail="item" :colors="colors" @select="select" :tip="item.isSubmit ? '已提交' : ''">
              <template #op>
                <u-button
                  type="primary"
                  shape="circle"
                  :color="colors"
                  :text="item.isSubmit ? '查看' : '选择'"
                  :plain="false"
                  size="small"
                  @tap="select(item)" />
              </template>
            </project-item>
          </view>
        </u-swipe-action-item>
      </u-swipe-action>
    </mescroll-body>

    <ut-fixed safe-area-inset position="bottom">
      <view class="margin">
        <u-button
          type="primary"
          shape="circle"
          :color="colors"
          text="新增项目"
          size="normal"
          @click="handleAdd"></u-button>
      </view>
    </ut-fixed>

    <u-popup
      :show="showProject"
      mode="bottom"
      round="10"
      :closeable="true"
      :safe-area-inset-bottom="false"
      :mask-close-able="true"
      close-icon-pos="top-left"
      :z-index="998"
      :overlay-style="{ zIndex: 998 }"
      @close="handlePopupClose">
      <view class="pop-title">项目选择</view>
      <scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}">
        <view style="max-height: 75vh">
          <u-checkbox-group v-model="checkboxValue1" placement="column">
            <view
              v-for="(item, index) in projectList"
              :key="item.id || index"
              class="flex justify-between padding-lr-xl padding-tb-sm">
              <view class="flex-sub">
                <view class="text-df text-bold">
                  <text class="project-code-tag">{{ item.govCode }}</text>
                  {{ item.name }}
                  <text v-if="getConflictInfo(item)" class="conflict-info">{{ getConflictInfo(item) }}</text>
                </view>
                <view class="text-xs text-gray">
                  <text>{{ item.projectTypeName }}</text>
                  <text v-if="item.minDuration" class="margin-left-xs">{{ item.minDuration }}--</text>
                  <text v-if="item.maxDuration">{{ item.maxDuration }}</text>
                  <text v-if="item.minDuration || item.maxDuration">分钟</text>
                  <text v-if="item.monthTimes > 0" class="margin-left-xs service-times">
                    本月已服务{{ item.monthTimes }}次
                  </text>
                  <text v-if="item.suggestRemark" class="margin-left-xs suggest-remark">
                    {{ item.suggestRemark }}
                  </text>
                </view>
              </view>
              <view>
                <u-checkbox
                  v-model="item.checked"
                  :activeColor="colors"
                  size="big"
                  :name="item.id"
                  @change="onProjectSelect(item)"></u-checkbox>
              </view>
            </view>
          </u-checkbox-group>
        </view>
      </scroll-view>
      <view safe-area-inset position="bottom">
        <view class="margin">
          <u-button
            type="primary"
            shape="circle"
            :color="colors"
            text="增加选中"
            size="normal"
            :disabled="!checkboxValue1.length"
            @click="handleSave"></u-button>
        </view>
      </view>
    </u-popup>

    <ut-login-modal :colors="colors"></ut-login-modal>
  </ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'
import ProjectItem from '@/pagesA/components/project-item.vue'

export default {
  mixins: [MescrollMixin], // 使用mixin
  components: {
    MescrollBody,
    ProjectItem,
  },
  options: {
    styleIsolation: 'shared',
  },
  data() {
    return {
      colors: '',
      topWrapHeight: 0,
      upOption: {
        noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
        empty: {
          icon: require('@/pagesA/components/image/nodata.png'),
          tip: '~ 没有数据 ~', // 提示
        },
      },
      pageReq: {
        pagesize: 100,
        pageindex: 1,
        key: '',
      },
      firstLoad: true,
      dataList: [],
      workId: '',
      showProject: false,
      projectData: [],
      checkboxValue1: [],
      checked: false,
      conflictCache: {}, // 冲突信息缓存
    }
  },
  onShow() {
    this.setData({ colors: app.globalData.newColor })
  },
  computed: {
    ...mapState({
      commKey: (state) => state.init.template.commKey,
      token: (state) => state.user.token,
      userInfo: (state) => state.user.info,
      community: (state) => state.init.community,
    }),
    // 计算属性：项目列表（用于优化渲染）
    projectList() {
      return this.projectData.info || []
    },
  },
  onLoad: async function (options) {
    await this.$onLaunched
    if (options.workId) {
      this.workId = options.workId
    }
    this.getAllProject()
  },
  methods: {
    getHeight(h) {
      this.topWrapHeight = h
    },
    /*下拉刷新的回调 */
    downCallback() {
      this.pageReq.pageindex = 1
      this.mescroll.resetUpScroll()

      this.$refs['swipeUserList'].closeAll()
    },
    async upCallback(page) {
      if (!this.workId) return
      this.$ut
        .api('mang/care/work/project/ruleListpg', {
          communityId: this.community.id,
          workId: this.workId,
          ...this.pageReq,
        })
        .then(({ data }) => {
          setTimeout(
            () => {
              this.mescroll.endBySize(data.info.length, data.record)
            },
            this.firstLoad ? 0 : 500
          )
          this.firstLoad = false

          if (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表
          this.pageReq.pageindex++
          this.dataList = this.dataList.concat(data.info)
        })
        .catch((e) => {
          this.pageReq.pageindex--
          this.mescroll.endErr()
        })
    },

    emptyClick() {},
    getActionOption(item) {
      const btnSubmitCancel = {
        text: '取消项目',
        code: 'submitCancel',
        style: {
          backgroundColor: '#ffaa7f',
        },
      }

      const btnDelete = {
        text: '删除项目',
        code: 'delete',
        style: {
          backgroundColor: '#f56c6c',
        },
      }

      let data = []

      if (item.isSubmit) {
        data.push(btnSubmitCancel)
      } else {
        data.push(btnDelete)
      }

      return data
    },
    longpress(item) {
      console.log(item)
    },
    select(item) {
      //this.$tools.routerTo('/pagesA/person/my-apply/info', { workId: item.id })
    },

    deleteProject(id) {
      return new Promise((resolve, reject) => {
        this.$ut
          .api('mang/care/work/project/delete', {
            communityId: this.community.id,
            workId: this.workId,
            ids: [id],
          })
          .then((res) => {
            resolve(res)
          })
          .catch((err) => {
            reject(err)
          })
      })
    },
    actionOp(data) {
      let that = this
      let content = ''
      if (data.code == 'delete') content = '确认删除该项目吗？'
      uni.showModal({
        title: '操作提示',
        content: content,
        success: (res) => {
          if (res.confirm) {
            if (data.code == 'delete') {
              this.deleteProject(data.name).then(() => {
                let objIndex = this.dataList.findIndex((u) => u.id == data.name)
                this.dataList.splice(objIndex, 1)
                uni.$emit('refreshList')
              })
            }
          } else if (res.cancel) {
          }
          this.$refs['swipeUserList'].closeAll()
        },
      })
    },
    handleAdd() {
      this.showProject = true
      this.$nextTick(() => {
        this.autoSelectExistingProjects()
      })
    },
    handleSave() {
      this.$ut
        .api('mang/care/work/project/save', {
          communityId: this.community.id,
          workId: this.workId,
          projectIds: this.checkboxValue1,
        })
        .then(() => {
          this.showProject = false
          this.downCallback()
          uni.$emit('refreshList')
          setTimeout(() => {
            uni.showToast({
              title: '保存成功',
            })
          }, 100)
        })
    },
    async getAllProject() {
      if (!this.workId) return
      const { data } = await this.$ut.api('mang/care/work/project/allRuleListpg', {
        communityId: this.community.id,
        workId: this.workId,
      })
      this.projectData = data
      this.preloadConflictCache()
      this.autoSelectExistingProjects()
    },
    preloadConflictCache() {
      if (!this.projectData.info || !this.projectData.info.length) return
      this.conflictCache = {}
      const projects = this.projectData.info
      projects.forEach((item) => {
        if (!item.repelIds || !item.repelIds.length) return
        const emptyCacheKey = `${item.id}_`
        this.conflictCache[emptyCacheKey] = ''
        projects.forEach((otherItem) => {
          if (item.id === otherItem.id) return
          const singleSelectKey = `${item.id}_${otherItem.id}`
          if (item.repelIds.includes(otherItem.id)) {
            this.conflictCache[singleSelectKey] = `（与"${otherItem.name}"冲突）`
          } else {
            this.conflictCache[singleSelectKey] = ''
          }
        })
      })
    },
    getConflictInfo(item) {
      if (!this.showProject) return
      if (!item.repelIds || !item.repelIds.length || !this.projectData.info) {
        return ''
      }
      if (this.checkboxValue1.length <= 1) {
        const singleSelectKey =
          this.checkboxValue1.length === 0 ? `${item.id}_` : `${item.id}_${this.checkboxValue1[0]}`
        if (this.conflictCache[singleSelectKey] !== undefined) {
          return this.conflictCache[singleSelectKey]
        }
      }
      const cacheKey = `${item.id}_${this.checkboxValue1.join(',')}`
      if (this.conflictCache[cacheKey] !== undefined) {
        return this.conflictCache[cacheKey]
      }
      const conflictProjects = this.checkboxValue1.filter(
        (selectedId) => item.repelIds.includes(selectedId) && selectedId !== item.id
      )
      let result = ''
      if (conflictProjects.length > 0) {
        const firstConflictId = conflictProjects[0]
        const conflictItem = this.projectData.info.find((p) => p.id === firstConflictId)
        const conflictName = conflictItem ? conflictItem.name : ''
        result = conflictName ? `（与"${conflictName}"冲突）` : ''
      }
      this.conflictCache[cacheKey] = result
      return result
    },
    onProjectSelect(item) {
      const keysToDelete = Object.keys(this.conflictCache).filter((key) => {
        const parts = key.split('_')
        return parts.length > 2 || (parts.length === 2 && parts[1].includes(','))
      })
      keysToDelete.forEach((key) => {
        delete this.conflictCache[key]
      })
    },
    handlePopupClose() {
      this.showProject = false
      this.checkboxValue1 = []
    },
    autoSelectExistingProjects() {
      if (!this.projectData.info || !this.projectData.info.length) return
      this.checkboxValue1 = []
      const existingProjectIds = this.dataList.map((item) => item.projectId)
      this.projectData.info.forEach((item) => {
        if (existingProjectIds.includes(item.id)) {
          this.$set(item, 'checked', true)
          if (!this.checkboxValue1.includes(item.id)) {
            this.checkboxValue1.push(item.id)
          }
        } else {
          this.$set(item, 'checked', false)
        }
      })
    },
  },
}
</script>
<style></style>
<style lang="scss" scoped>
.page {
  background-color: #fff;
}

.pop-title {
  padding-top: 20rpx;
  text-align: center;
}

/deep/.u-checkbox__icon-wrap {
  width: 48rpx;
  height: 48rpx;
  span {
    font-size: 40rpx;
  }
}

.project-code-tag {
  display: inline-block;
  font-weight: bold;
  background-color: #f0f0f0;
  color: #666;
  padding: 2rpx 8rpx;
  border-radius: 4rpx;
  margin-right: 8rpx;
}

.conflict-info {
  color: #ff9797;
  font-size: 22rpx;
}

.service-times {
  color: #5db1ff;
  font-weight: 500;
}

.suggest-remark {
  color: #ffa435;
  font-weight: 500;
}
</style>
