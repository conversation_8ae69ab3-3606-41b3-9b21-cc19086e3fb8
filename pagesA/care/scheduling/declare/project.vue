<template>
	<view >
		<scroll-view scroll-y="true" class="scroll-box" @touchmove.stop.prevent="() => {}" style="max-height: 60vh;">
			<u-checkbox-group v-model="checkboxValue1"  placement="column">
				<template v-for="(item,index) in projectData">
					<view class="flex justify-between padding-lr-xl padding-tb-sm">
						<view>
							<view class="text-df text-bold">
								{{item.name}}
								<text class="text-no text-sm text-gray">(编号：{{item.govCode}})</text>
							</view>
							<view class="text-xs text-gray">
								<text>{{item.projectTypeName}}</text>
								<text v-if="item.minDuration" class="margin-left-lg">{{item.minDuration}}--</text>
								<text v-if="item.maxDuration">{{item.maxDuration }}</text>
								<text v-if="item.minDuration || item.maxDuration">分钟</text>
								
							</view>
						</view>
						<view>
							<u-checkbox v-model="item.checked" :activeColor="colors" size="big" :name="item.id"></u-checkbox>
						</view>
					</view>
				</template>
				</u-checkbox-group>
		</scroll-view>
		<view >
			<view class="margin">
				<u-button type="primary" shape="circle" :color="colors" text="选择选中服务项目" size="normal" :disabled="!checkboxValue1.length"  @click="handleSave"></u-button>
			</view>
		</view>
		
		
	</view>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import ProjectItem from '@/pagesA/components/project-item.vue'

export default {
	mixins: [], // 使用mixin
	components: {
		ProjectItem,
	},
	props:{
		projectData:{
			type:Array,
			default:()=>[]
		},
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			topWrapHeight: 0,
			upOption: {
				noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					icon: require('@/pagesA/components/image/nodata.png'),
					tip: '~ 没有数据 ~', // 提示
				},
			},
			pageReq: {
				pagesize: 100,
				pageindex: 1,
				key: '',
			},
			firstLoad:true,
			workId:'',
			showProject:false,
			checkboxValue1:[],
			checked:false,
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched
	},
	mounted() {
		this.checkboxValue1=[]
		if(this.projectData && this.projectData.length){
			this.projectData.forEach(item=>{
				if(item.checked) this.checkboxValue1.push(item.id)
			})
		}
	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		/*下拉刷新的回调 */
		downCallback() {
			this.pageReq.pageindex = 1
			this.mescroll.resetUpScroll()
			
			this.$refs['swipeUserList'].closeAll()
		},

		
		handleSave(){
			if(!this.checkboxValue1 || !this.checkboxValue1.length)return
			let arr=[]
			this.checkboxValue1.forEach(projectId=>{
				let obj=this.projectData.find(u=>u.id==projectId)
				if(obj) arr.push((obj))
			})
			this.$emit('selectProject',arr)
		},


	},
}
</script>
<style>


</style>
<style lang="scss" scoped>

.page {
	background-color: #fff;
}


.pop-title {
	padding-top: 20rpx;
	text-align: center;
}

/deep/.u-checkbox__icon-wrap{
	width:48rpx;
	height:48rpx;
	span{
		font-size: 40rpx;
	}
}

</style>
