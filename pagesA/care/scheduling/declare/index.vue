<template>
	<ut-page>
		<ut-top class="top-warp" bg-color="#fff" @topHeight="getHeight">
			<f-navbar fontColor="#fff" :bgColor="colors" title="打卡异常" navbarType="2"></f-navbar>
		</ut-top>
		<mescroll-body
			ref="mescrollRef"
			:top="topWrapHeight+'px'"
			:top-margin="-topWrapHeight+'px'"
			bottom="0"
			:up="upOption"
			:safearea="true"
			@init="mescrollInit"
			@down="downCallback"
			@up="upCallback"
			@emptyclick="emptyClick"
		>
			<u-swipe-action ref="swipeUserList">
			<template v-for="(item,index) in dataList">	
				
				<view class="cu-card margin-lr-sm margin-tb-sm radius padding-lr-sm padding-tb-sm bg-white text-sm text-content" @click="itemClick(item)">
<!-- 					<u-swipe-action-item
						:key="index"
						:name="item.id"
						:options="getActionOption(item)"
						@longpress="longpress(item)"
						@click="actionOp"> -->
<!-- 					<view>
						<text>护理员：</text>
						<text class="text-df text-bold">{{item.attendantName}}</text>
						<text class="margin-left-lg">({{item.attendantPhone}})</text>
					</view> -->
					<view>
						<text>客户：</text>
						<text class="text-df text-bold">{{item.name}}</text>
						<text v-if="item.sex && item.sex==1" class="margin-left-xs">男</text>
						<text v-else-if="item.sex && item.sex==2" class="margin-left-xs">女</text>
						<text class="margin-left-lg">({{item.phone}})</text>
					</view>
					<view v-if="item.idcard">
						<text>证件：</text>
						<text class="">{{item.idcard}}</text>
					</view>
					<view>
						<text>排班计划：</text>
						<text class="text-df text-bold margin-left">{{item.schedulingDate}}</text>
						<text class="text-df text-bold">{{item.schedulingBegin}}</text>
						<text class="margin-lr-xs">-</text>
						<text class="text-df text-bold">{{item.schedulingEnd}}</text>
						<text class="margin-left text-xs">(</text>
						<text class="text-xs">{{item.schedulingDuration}}</text>
						<text class="text-xs">分钟)</text>
					</view>
					<view class="flex justify-between">
						<view>
							<view>
								<text>签到时间：</text>
								<text v-if="item.checkInTime" :style="{color:colors}">{{item.checkInTime}}</text>
								<text v-else class="text-red">未签到</text>
							</view>					
							<view>
								<text>签退时间：</text>
								<text v-if="item.checkOutTime" :style="{color:colors}">{{item.checkOutTime}}</text>
								<text v-else class="text-red">未签退</text>
							</view>
							<view class="flex">
								<text>服务项目：</text>
								<view v-if="item.projectTotalMaxDuration>=item.schedulingDuration && item.projectTotalMinDuration<=item.schedulingDuration">
									<text :style="{color:colors}">共{{item.projects.length}}项</text>
								</view>
								<view v-else class="flex">
									<view v-if="item.projects && item.projects.length" class="text-red">
										<text>共{{item.projects.length}}项</text>
										<text class="margin-left-xs">(时长不符)</text>
									</view>
									<text v-else class="text-red">还未选择</text>
								</view>
							</view>
							<view>
								<text>护理资料：</text>
								<text v-if="item.uploadData" :style="{color:colors}">已上传</text>
								<text v-else class="text-red">未上传护理资料</text>
							</view>
						</view>
						<view v-if="!item.isApplyError">
							<u-button type="primary" shape="circle" :color="colors" text="异常申报" size="mini" @click="select(item)"></u-button>
						</view>
					</view>
					
					<view v-if="item.isApplyError">
						<view>
							<text>审核状态：</text>
							<text v-if="item.applyErrorAuditState==0">未审核</text>
							<text v-else-if="item.applyErrorAuditState==1">审核通过</text>
							<text v-else-if="item.applyErrorAuditState==2">审核未通过</text>
						</view>
					</view>
					
					<!-- </u-swipe-action-item> -->
				</view>
				
				
			</template>
			
			</u-swipe-action>
		</mescroll-body>
		
		<u-popup :show="showDeclareCancel" mode="bottom" round="10" :closeable="true" :safe-area-inset-bottom="false"
				 :mask-close-able="true" close-icon-pos="top-left" :z-index="998" :overlay-style="{zIndex:998}" @close="showDeclareCancel=false">
			<view class="pop-title">审核情况</view>
			<view style="max-height: 75vh;min-height: 30vh;" class="padding-sm">
				<view class="text-lg text-center text-bold margin-top">{{selectItem.name}}</view>
				<view v-if="selectItem.submitTime" class="text-lg text-center">提示时间:{{selectItem.submitTime}}</view>
				<view class="text-lg  text-center margin-top-xl">
					<text>审核状态：</text>
					<text v-if="selectItem.applyErrorAuditState==0">未审核</text>
					<text v-else-if="selectItem.applyErrorAuditState==1">审核通过</text>
					<text v-else-if="selectItem.applyErrorAuditState==2">审核未通过</text>
				</view>
				<view class="text-lg text-center margin-top-xl padding-xl">
					<u-button type="primary" shape="circle" :color="colors" text="取消申报"  @click="declareCancel"></u-button>
				</view>
			</view>
		</u-popup>
		
		<ut-login-modal :colors="colors"></ut-login-modal>
	</ut-page>
</template>

<script>
var app = getApp()
import { mapState } from 'vuex'
import MescrollBody from '@/components/mescroll-uni/mescroll-body/mescroll-body.vue'
import MescrollMixin from '@/components/mescroll-uni/mescroll-uni/mescroll-mixins.js'


export default {
	mixins: [MescrollMixin], // 使用mixin
	components: {
		MescrollBody,
	},
	options: {
		styleIsolation: 'shared',
	},
	data() {
		return {
			colors: '',
			topWrapHeight: 0,
			upOption: {
				noMoreSize: 5, //如果列表已无数据,可设置列表的总数量要大于半页才显示无更多数据;避免列表数据过少(比如只有一条数据),显示无更多数据会不好看; 默认5
				empty: {
					icon: require('@/pagesA/components/image/nodata.png'),
					tip: '~ 没有数据 ~', // 提示
				},
			},
			pageReq: {
				pagesize: 20,
				pageindex: 1,
				key: '',
			},
			firstLoad:true,
			dataList:[],
			auditState:0,			
			
			showDeclareCancel:false,
			selectItem:{},
		}
	},
	onShow() {
		this.setData({ colors: app.globalData.newColor })
	},
	computed: {
		...mapState({
			commKey: state => state.init.template.commKey,
			token: state => state.user.token,
			userInfo: state => state.user.info,
			community: state => state.init.community,
		}),
	},
	onLoad: async function (options) {
		await this.$onLaunched

	},
	methods: {
		getHeight(h) {
			this.topWrapHeight = h
		},
		/*下拉刷新的回调 */
		downCallback() {
			this.pageReq.pageindex = 1
			this.mescroll.resetUpScroll()
		},
		async upCallback(page) {
			this.isShow = false

			this.$ut.api('mang/care/customer/scheduling/declare/listpg', {
				communityId:this.community.id,
				...this.pageReq,
			}).then(({data}) => {		
				setTimeout(()=>{
					this.mescroll.endBySize(data.info.length, data.record)
				},this.firstLoad?0:500)
				this.firstLoad=false
				
				if (this.pageReq.pageindex == 1) this.dataList = [] //如果是第一页需手动制空列表
				this.pageReq.pageindex++
				this.dataList = this.dataList.concat(data.info)	

			}).catch(e => {
				this.pageReq.pageindex--
				this.mescroll.endErr()
			})
		},

		longpress(item) {
			console.log(item)
		},
		emptyClick() {
		},
		jumpCustomer(){			
			this.$tools.routerTo('/pagesA/care/customer/index', {})
		},
		getActionOption(item) {
			const btnApplyCancel = {
				text: '审核通过',
				code:'auditOK',
				style: {
					backgroundColor: '#ffaa7f'
				}
			}
			let data = []
			
			if (item.auditState!=1) {
				data.push(btnApplyCancel)
			}
			
			return data
		},
		actionOp(data) {
			let content = ''
			if (data.code == 'auditOK') content = '确认审核通过吗？'
			uni.showModal({
				title: '操作提示',
				content: content,
				success: res=> {
					if (res.confirm) {
						if (data.code == 'auditOK') {
							this.auditOK(data.name).then(()=>{
								let objIndex = this.dataList.findIndex(u => u.id == data.name)
								this.dataList.splice(objIndex, 1)
							})							
						}
					}
					this.$refs['swipeUserList'].closeAll()
				}
			})
		},
		auditOK(id){
			return new Promise((resolve, reject) => {
				this.$ut.api('mang/care/customer/scheduling/audit/audit', {
					communityId:this.community.id,
					ids:[id],
					auditState:1,
				}).then(res=>{
					resolve(res)
				}).catch(err=>{
					reject(err)
				})
			})
		},
		select(item) {
			this.$tools.routerTo('/pagesA/care/scheduling/declare/apply', { declareId: item.id })
		},
		itemClick(item){
			this.selectItem=item
			if(this.selectItem.applyErrorAuditState==0 && this.selectItem.isApplyError){
				this.showDeclareCancel=true
			}
			
			//this.showDeclareCancel=true
		},
		declareCancel(){
			uni.showLoading({
				title:'请等待...'
			})
			this.$ut.api('mang/care/customer/scheduling/declare/applyCancel', {
				communityId:this.community.id,
				ids:[this.selectItem.id],
			}).then(()=>{
				this.showDeclareCancel=false
				this.downCallback()
			}).finally(()=>{uni.hideLoading()})
		}
	},
}
</script>
<style>


</style>
<style lang="scss" scoped>

.tip{
	position: absolute;
	bottom: 0;
	width:100%;
}

.pop-title {
	padding-top: 20rpx;
	text-align: center;
}

/deep/.u-checkbox__icon-wrap{
	width:48rpx;
	height:48rpx;
	span{
		font-size: 40rpx;
	}
}

</style>
