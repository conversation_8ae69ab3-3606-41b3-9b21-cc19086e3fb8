{
    "name" : "UT框架",
    "appid" : "__UNI__C8A82F7",
    "description" : "",
    "versionName" : "1.0.0",
    "versionCode" : "100",
    "transformPx" : false,
    "lazyCodeLoading" : "requiredComponents",
	"sassImplementationName":"node-sass",
    "app-plus" : {
        "popGesture" : "none",
        "optimization" : {
            "subPackages" : true
        },
        "runmode" : "liberate", // 开启分包优化后，必须配置资源释放模式
        "usingComponents" : true,
        "nvueCompiler" : "uni-app",
        "compilerVersion" : 3,
        "splashscreen" : {
            "alwaysShowBeforeRender" : true,
            "waiting" : true,
            "autoclose" : true,
            "delay" : 0
        },
        "modules" : {
            "VideoPlayer" : {},
            "OAuth" : {},
            "Payment" : {},
            "Share" : {},
            "Push" : {}
        },
        "distribute" : {
            "android" : {
                "permissions" : [
                    "<uses-feature android:name=\"android.hardware.camera\"/>",
                    "<uses-feature android:name=\"android.hardware.camera.autofocus\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_COARSE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_FINE_LOCATION\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.ACCESS_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CALL_PHONE\"/>",
                    "<uses-permission android:name=\"android.permission.CAMERA\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_NETWORK_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.CHANGE_WIFI_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.FLASHLIGHT\"/>",
                    "<uses-permission android:name=\"android.permission.GET_ACCOUNTS\"/>",
                    "<uses-permission android:name=\"android.permission.INTERNET\"/>",
                    "<uses-permission android:name=\"android.permission.MODIFY_AUDIO_SETTINGS\"/>",
                    "<uses-permission android:name=\"android.permission.MOUNT_UNMOUNT_FILESYSTEMS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_LOGS\"/>",
                    "<uses-permission android:name=\"android.permission.READ_PHONE_STATE\"/>",
                    "<uses-permission android:name=\"android.permission.RECORD_AUDIO\"/>",
                    "<uses-permission android:name=\"android.permission.VIBRATE\"/>",
                    "<uses-permission android:name=\"android.permission.WAKE_LOCK\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_EXTERNAL_STORAGE\"/>",
                    "<uses-permission android:name=\"android.permission.WRITE_SETTINGS\"/>"
                ],
                "abiFilters" : [ "armeabi-v7a", "arm64-v8a", "x86" ]
            },
            "ios" : {
                "capabilities" : {
                    "entitlements" : {
                        "com.apple.developer.associated-domains" : [ "applinks:static-269aebcc-cf4f-4cb2-a95f-d64110590124.bspapp.com" ]
                    }
                },
                "dSYMs" : false
            },
            "sdkConfigs" : {
                "ad" : {},
                "share" : {
                    "weixin" : {
                        "appid" : "wx6c964594dd5eafa0",
                        "UniversalLinks" : "https://static-269aebcc-cf4f-4cb2-a95f-d64110590124.bspapp.com/uni-universallinks/__UNI__C8A82F7/"
                    }
                },
                "maps" : {},
                "oauth" : {
                    "weixin" : {
                        "appid" : "wx6c964594dd5eafa0",
                        "appsecret" : "730f84e34bb109f2e27810dc534950ed",
                        "UniversalLinks" : "https://static-269aebcc-cf4f-4cb2-a95f-d64110590124.bspapp.com/uni-universallinks/__UNI__C8A82F7/"
                    }
                },
                "payment" : {
                    "weixin" : {
                        "__platform__" : [ "ios", "android" ],
                        "appid" : "wx6c964594dd5eafa0",
                        "UniversalLinks" : "https://static-269aebcc-cf4f-4cb2-a95f-d64110590124.bspapp.com/uni-universallinks/__UNI__C8A82F7/"
                    }
                },
                "push" : {}
            },
            "icons" : {
                "android" : {
                    "hdpi" : "static/imgs/ut-logo.png",
                    "xhdpi" : "static/imgs/ut-logo.png",
                    "xxhdpi" : "static/imgs/ut-logo.png"
                }
            }
        }
    },
    "quickapp" : {},
    "mp-weixin" : {
        "appid" : "wx10c2883e3b0beccb",
        "setting" : {
            "urlCheck" : false,
            "minified" : true,
            "postcss" : false
        },
        "usingComponents" : true,
        "permission" : {
            "scope.userFuzzyLocation" : {
                "desc" : "获取当前位置"
            },
            "scope.userLocation" : {
                "desc" : "获取当前位置"
            }
        },
        // "requiredPrivateInfos" : [ "chooseAddress", "getFuzzyLocation", "chooseLocation" ],
        "requiredPrivateInfos" : [ "chooseAddress", "chooseLocation", "getLocation" ],
        "plugins" : {},
        "optimization" : {
            "subPackages" : true
        }
    },
    "mp-alipay" : {
        "usingComponents" : true
    },
    "mp-baidu" : {
        "usingComponents" : true
    },
    "mp-toutiao" : {
        "usingComponents" : true
    },
    "h5" : {
        "router" : {
            "mode" : "history",
            "base" : "/"
        },
        "sdkConfigs" : {
            "maps" : {
                "tencent" : {
                    "key" : "SFABZ-WANWW-FISRY-3IGTF-HV7RE-YSFTI"
                },
                "qqmap" : {
                    "key" : "KF6BZ-GRYKJ-LCQFU-D7KLZ-UZBFV-PWBWC"
                }
            }
        },
        "devServer" : {
            "https" : false
        },
        "optimization" : {
            "treeShaking" : {
                "enable" : true
            }
        },
        "template" : "static/main.html"
    },
    "_spaceID" : "269aebcc-cf4f-4cb2-a95f-d64110590124"
}
