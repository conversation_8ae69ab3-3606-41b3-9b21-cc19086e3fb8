<template>
	<ut-page>
		<f-navbar fontColor="#fff" :bgColor="colors" title="我的陪诊资料" navbarType="6">
		</f-navbar>
		<view class="acc-body">
			<view class="card-body">
				<view class="item">
					<view class="item-left">陪诊城市：</view>
					<view class="item-right">{{employeeInfo.cityName}}</view>
				</view>
				<!-- 			<view class="item">
				<view class="item-left">陪诊区域：</view>
				<view class="item-right">
						<u-checkbox-group v-model="info.areas"  @change="checkboxChange" placement="row">
							<u-checkbox :customStyle="{marginLeft: '16rpx',marginTop: '20rpx'}"
								v-for="(item, index) in areaList" :key="index" :checked="myArea(item)" :label="item.name"
								:name="item.id" :activeColor="colors">
							</u-checkbox>
						</u-checkbox-group>
				</view>
				
			</view> -->
				<view class="item">
					<view class="item-left">身份证号：</view>
					<view class="item-right">{{employeeInfo.identityCard}}</view>
				</view>
				<view class="item">
					<view class="item-left">手机号码：</view>
					<view class="item-right">{{employeeInfo.phone}}</view>
				</view>
				<view class="item">
					<view class="item-left">性别：</view>
					<view class="item-right">
						<view>
							<text class="sex" v-if="employeeInfo.sex == 1">男</text>
							<text class="sex" v-if="employeeInfo.sex == 2">女</text>
						</view>
					</view>
				</view>
				<view class="item">
					<view class="item-left">展示称呼：</view>
					<view class="item-right">{{employeeInfo.stageName}}</view>
				</view>
<!-- 				<view class="item">
					<view class="item-left">形象照片：</view>
					<view class="item-right phone">
						<u-lazy-load class="s-img" height="100" width="100" border-radius="4"
							@click="$tools.previewImage([employeeInfo.imgHead+'&width=800'])"
							:image="$tools.showImg(employeeInfo.imgHead,200)"></u-lazy-load>
					</view>
				</view> -->

			</view>
			<view class="card-body">
				<view class="item">
					<view class="item-left">当前接单状态</view>
					<view class="item-right">
						<u-button v-if="employeeInfo.receiveWork" type="success" text="关闭接单状态"
							@click="setWork(false)"></u-button>
						<u-button v-else type="info" text="开启接单状态" @click="setWork(true)"></u-button>
						<view class="message" v-if="employeeInfo.receiveWork">关闭后平台将不再给您派单</view>
						<view class="message" v-else>开启后，平台才能给您派单</view>
					</view>
				</view>
			</view>
		</view>

	</ut-page>
</template>

<script>
	var app = getApp();
	import {
		mapState,
	} from 'vuex'

	export default {
		components: {},
		data() {
			return {
				colors: '',
				showUserAgreement: false,
				agreementInfo: {},
				checkboxValue: [],
				pageType: '',
				employeeInfo: {
					id: '',
					cityId: '',
					cityName: '',
					areas: [],
					identityCard: '',
					phone: '',
					sex: '',
					stageName: '',
					imgHead: '',
					identityCardImg1: '',
					identityCardImg2: '',
				},
				cityList: [],
			}
		},
		onShow() {
			this.setData({
				colors: app.globalData.newColor
			});
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				userInfo: state => state.user.info,
			}),

		},
		onLoad: async function(options) {
			if (options.type) this.pageType = options.type

			this.getMyInfo()
		},
		methods: {

			getMyInfo() {
				this.$ut.api('hospital/employee/myInfo', {
					commKey: this.commKey,
				}).then(res => {
					if (res.data) {
						this.employeeInfo = res.data
						this.$set(this.employeeInfo, 'areas', this.employeeInfo.areaIds)
					}
				})
			},
			getCityList() {
				this.$ut.api('hospital/city/list', {
					commKey: this.commKey,
				}).then(res => {
					this.cityList = res.data
				})
			},
			setWork(type) {
				let that = this
				if (type == false) {
					uni.showModal({
						title: '提示',
						content: '取消后平台将无法给您派单，您确定取消吗?',
						success: function(res) {
							if (res.confirm) {
								that.setWorkApi(type)
							} else if (res.cancel) {

							}
						}
					});
				} else {
					that.setWorkApi(type)
				}

			},
			setWorkApi(type) {
				this.$ut.api('hospital/employee/receiveWork', {
					commKey: this.commKey,
					receiveWork: type,
				}).then(res => {
					this.employeeInfo.receiveWork = type
				})
			},

		}
	}
</script>

<style lang="scss" scoped>
	.acc-body {
		padding: 20rpx 20rpx 20rpx 20rpx;
		// margin-bottom: 200rpx;
	}

	.item {
		display: flex;
		justify-content: space-between;
		align-items: center;
		padding: 30rpx 0;
		border-bottom: 1rpx solid rgb(240, 240, 240);

		&:last-child {
			border-bottom: none;
		}

		.item-left {
			color: gray;
			letter-spacing: 2rpx;
			min-width: 160rpx;
		}

		.item-right {
			/deep/ .ut-image-upload-list {
				justify-content: flex-end;
			}

			.phone {
				display: flex;
				justify-content: flex-end;
			}

			.message {
				font-size: 20rpx;
				color: #ccc;
			}

			.ipt {
				letter-spacing: 2rpx;
				color: rgba(#000, 0.4);
				text-align: right;
			}
		}
	}

	.card-body {
		border-radius: 14rpx;
		background-color: #fff;
		padding: 0 30rpx;
		margin-top: 20rpx;
	}

	.card-body:first-child {
		margin-top: 0;
	}
</style>