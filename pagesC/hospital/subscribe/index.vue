<template>
	<ut-page class="hospital-service-order">
		<f-navbar title="填写服务订单" fontColor="#000" bgColor="#fff" navbarType="6"/>
		<view class="hospital-service-order-bg"></view>
		<service-content :show.sync="showContent" :project-info="projectInfo" />
		<view class="service-order-body">
			<view class="order-progress">
				<u-line-progress :percentage="percentage" :showText="false" inactiveColor="#fff"></u-line-progress>
			</view>
			<view class="order-subsection">
				<tabs ref="tabs" :tabsData="list" :disable="false" bjColor="transparent" lineColor="transparent" fontColor="#ddd" activeColor="#fff"
					:bold="true" @change="subsectionChang" />
			</view>
			<!-- <view @click="aa">支付测试</view> -->
			<order 
				:colors="colors" 
				:project-info="projectInfo" 
				:shop-info="shopInfo" 
				:agreement="agreementInfo" 
				ref='order' v-show="current === 0" 
				@onShowContent="showContent = true" 
				@next="next" 
				@createOrder="createOrder"/>
			<pay-success v-show="current === 1" @next="next" />
			<service v-show="current === 2" @next="next" />
			<order-success v-show="current === 3" />
		</view>
		
	</ut-page>

</template>

<script>
	let app = getApp();
	import { mapState } from 'vuex'
	import ServiceContent from "../components/content.vue"
	import Tabs from "../components/order-tabs.vue"
	import Order from "./order.vue"
	import Service from "./service.vue"
	import PaySuccess from "./pay-success.vue"
	import OrderSuccess from "./order-success.vue"
	export default {
		components: {
			ServiceContent,
			Order,
			PaySuccess,
			Service,
			OrderSuccess,
			Tabs
		},
		data() {
			return {
				colors:'',
				showContent: true,
				payQueryOver:false,
				isPaying:false,
				list: [{
					name: "填写订单"
				}, {
					name: "在线支付"
				}, {
					name: "专人服务"
				}, {
					name: "服务完成"
				}],
				current: 0,
				orderInfoComplete: false,
				projectId:'',
				projectInfo:{},
				shopInfo:{},
				agreementInfo:{},
			}
		},
		computed: {
			...mapState({
				commKey: state => state.init.template.commKey,
				userInfo: state => state.user.info,
			}),
			percentage() {
				let progress = (this.current + 1) * 25
				return progress - 1
			}
		},
		options: {
			styleIsolation: 'shared'
		},
		onShow: function() {
			let colors = app.globalData.newColor;
			this.setData({
				colors: colors
			});
		},
		onLoad:function(options){
			if(options.id) this.projectId=options.id
			if(!this.projectId) return 
			
			this.getProjectInfo()
			this.getShopInfoByProject()
			this.getAgreement()
		},
		methods: {
			subsectionChang(index, item) {
				this.current = index
				if (index === 0) this.orderInfoComplete = false
			},
			next() {
				this.orderInfoComplete = true
				this.$refs.tabs.onTabIndex(this.current + 1, {})
			},
			getProjectInfo() {
				this.$ut.api('hospital/shop/project/info',{
					commKey:this.commKey,
					id:this.projectId,
				}).then(res=>{
					this.projectInfo=res.data
				})
			},
			getShopInfoByProject(){
				this.$ut.api('hospital/shop/infoByProject',{
					commKey:this.commKey,
					shopProjectId:this.projectId,
				}).then(res=>{
					this.shopInfo=res.data
				})
			},
			getAgreement(){
				this.$ut.api('hospital/shop/agreement/pz',{
					commKey:this.commKey,
					code:'pz',
				}).then(res=>{
					this.agreementInfo=res.data
				})
			},
			createOrder(order){
				if(!this.shopInfo)return 
				if(!this.shopInfo.shop) return
				if(!this.shopInfo.shop.id) return
				
				this.isPaying=true
				uni.showLoading({
					title: "  生成支付订单  "
				})
				this.$ut.api('hospital/order/create',{
					commKey:this.commKey,
					shopId:this.shopInfo.shop.id,
					time:order.date,
					times:order.time,
					phone:order.phone,
					idcard:order.idcard,
					patient:order.people,
					remark:order.remark,
					projectIds:[this.projectId]
				}).then(res=>{
					
					this.payOrder(res.data)
					// uni.redirectTo({
					// 	url: '/pagesC/hospital/subscribe/pay?q=true&id='+res.data
					// })
				})
			},
			payOrder(orderId){
				let that=this
				uni.showLoading({
					title:'请稍等...'
				})
				this.$ut.api('hospital/order/pay',{
					commKey:this.commKey,
					orderId:orderId,
					wxOpenId:this.userInfo.wxOpenId,
					appType:2, //小程序支付
					type:1, //微信支付
				}).then(res=>{
					uni.hideLoading()
					const wechatPayData=res.data.wechatPayData
					wx.requestPayment({
						timeStamp: wechatPayData.timeStamp, 
						nonceStr: wechatPayData.nonceStr,   
						package: wechatPayData.package,     
						signType: wechatPayData.signType, 
						paySign: wechatPayData.paySign,        
					    success(res) {
							//查询订单
							that.payTimeQuery(orderId)
						},
					    fail(e) {
							that.payPause(orderId)
						}
					})
				}).catch(e=>{
					this.isPaying=false
					uni.hideLoading()
				})
			},
			payQuery(orderId){
				this.$ut.api('hospital/order/payQuery',{
					commKey:this.commKey,
					id:orderId,
					appType:2, //小程序支付
				}).then(res=>{
					this.payQueryOver=true
				})
			},
			payTimeQuery(orderId){
				let that=this
				uni.showLoading({
					title:'请稍等...'
				})
				setTimeout(()=>{
					that.payQuery(orderId)
				},2000)
				if(!that.payQueryOver){
					setTimeout(()=>{
						that.payQuery(orderId)
					},3000)
				}
				uni.hideLoading()
				setTimeout(()=>{
					uni.switchTab({
						url: '/pages/order/order'
					})
				},100)
				
			},
			payPause(orderId){
				uni.showLoading({
					title:'正在取消订单...'
				})
				this.$ut.api('hospital/order/payPause',{
					commKey:this.commKey,
					id:orderId,
					appType:2, //小程序支付
				}).then(res=>{
					this.isPaying=false
					uni.hideLoading()
				}).catch(e=>{
					this.isPaying=false
					uni.hideLoading()
				})
			},
			aa(){
				setTimeout(()=>{
					uni.switchTab({
						url: '/pages/order/order'
					})
				},100)
				
				// let that=this
				// that.$tools.routerTo('/pages/order/order')
			}
		}
	}
</script>

<style lang="scss" scoped>
	.hospital-service-order {
		position: relative;

		.hospital-service-order-bg::after {
			content: ' ';
			width: 750rpx;
			height: 260rpx;
			position: absolute;
			background: linear-gradient(to right, #00bc69, #00a3a8);
		}

		.service-order-body {
			padding: 40rpx 20rpx 20rpx;
			position: relative;

			.order-progress {
				padding: 18rpx;

				/deep/ .u-line-progress {

					.u-line-progress__background {
						height: 32rpx !important;
					}

					.u-line-progress__line {
						background-image: linear-gradient(to right, #00bc69, #00a3a8);
						top: 4rpx;
						left: 4rpx;
						height: 12rpx;
					}
				}
			}

			.order-subsection {
				margin: 18rpx 0 0 0;
			}

			/deep/ .u-subsection {
				.u-subsection--button__bar {
					background-color: transparent;
				}
			}
		}
	}
</style>
