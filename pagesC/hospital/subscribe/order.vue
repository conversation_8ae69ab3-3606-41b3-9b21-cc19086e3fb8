<template>
	<view class="hospital-order" :style="{'--colors-bg':colors+'30'}">
		<view class="service-content">
			<view class="service-content-icon">
				<u--image v-if="projectInfo.imgHead" :showLoading="true" :src="projectInfo.imgHead" width="44rpx"
					height="44rpx" radius="4rpx"></u--image>
				<text v-else class="iconfont icon-vip"></text>
			</view>
			<view class="service-content-title">{{projectInfo.name}}</view>
			<view class="service-content-info" @click="clickContent">
				<text class="iconfont icon-vip"></text>
				服务内容
			</view>
		</view>

		<view class="order-info">
			<view class="order-info-item">
				
					
				<view class="info-item-container">
					<!-- <view class="info-item-title hospital">就诊医院<text class="required">*</text></view> -->
					<view v-if="!firstBase">
						<text class="info-item-text info-selected">{{shopInfo.shop?shopInfo.shop.name:''}}</text>
					</view>
					<view v-else>
						<view @click="showHospital=true">
							<text v-if="hospitalInfo.id" class="info-item-text info-selected">{{hospitalInfo.name}}</text>
							<text v-else>请选择医院</text>
						</view>
					</view>
<!-- 					<text :class="['info-item-content',hospital ? '' : 'no-content']">
						{{ hospital ? hospital : '请选择就诊医院' }}
					</text>
					<text class="iconfont icon-right"></text> -->
				</view>
			</view>
			<view class="order-info-item">
				<view class="info-item-title">
					<view>就诊时间<text class="required">*</text></view>
					<view class="required-text" v-if="isChecked && !form.time">需要选择时间</view>
				</view>
				<view class="info-item-container info-time-box">
					<template v-for="(item,index) in projectInfo.timesList">
						<text class="info-item-text info-time" :class="{'info-selected':findSelected(item)}" :key="index" @click="infoItemClick(item)">{{item.title}}</text>
					</template>
					
					<!-- <text class="info-item-text info-time">下午13:00~18:00</text> -->
				</view>
			</view>
			<view class="order-info-item">
				<view class="info-item-title">
					<view>就诊日期<text class="required">*</text></view>
					<view class="required-text"  v-if="isChecked && !form.date">需要选择日期</view>
				</view>
				<view class="info-item-container" @click="showTime=true">
					 <text v-if="!dateValue" >选择日期</text>
					 <text v-else class="info-date">{{dateValue}}</text>
				</view>
			</view>
			<view class="order-info-item">
				<view class="info-item-title">
					<view>姓名<text class="required">*</text></view>
					<view class="required-text"  v-if="isChecked && !form.people">需要填写</view>
				</view>
				<view class="info-item-container">
					<input class="item-cotent-input" type="text" placeholder="请填写就诊人姓名"
						placeholder-class="input-placeholder-class" v-model="form.people" />
				</view>
			</view>
			<view class="order-info-item">
				<view class="info-item-title">
					<view>身份证号码<text class="required">*</text></view>
					<view class="required-text"  v-if="isChecked && !form.idcard">需要填写</view>
				</view>
				<view class="info-item-container">
					<input class="item-cotent-input" type="idcard" placeholder="请填写就诊人身份证号"
						placeholder-class="input-placeholder-class" v-model="form.idcard" />
				</view>
			</view>
			<view class="order-info-item">
				<view class="info-item-title">
					<view>联系电话<text class="required">*</text></view>
					<view class="required-text" v-if="isChecked && !form.phone">需要填写</view>
				</view>
				<view class="info-item-container">
					<input class="item-cotent-input" type="number" placeholder="请填写联系电话"
						placeholder-class="input-placeholder-class" v-model="form.phone" />
				</view>
			</view>
			<view class="order-info-item">
				<view class="info-item-title">地址
					
				</view>
				<view class="info-item-container address">
					<u--textarea v-model="form.address" height="40" placeholder="请填写或选择您所在地址" maxlength="150" autoHeight></u--textarea>
<!-- 					<input class="item-cotent-input" type="text" placeholder="请填写或选择您所在地址"
						placeholder-class="input-placeholder-class" v-model="form.address" /> -->
					<view class="gps cuIcon-icon icon-address-1" v-if="!form.address" @click.stop="getLocation">地图</view>
				</view>
			</view>
			
		</view>
		<view class="tip-message">带*号的请您务必填写</view>
		<view class="service-demand">
			
			<view class="demand-content">
				<view class="demand-title">服务需求</view>
				<u--textarea v-model="form.remark" height="40" placeholder="请简单描述您的需求..." confirmType="done" maxlength="300" count autoHeight></u--textarea>
				<!-- <textarea value="" placeholder="请简单描述您的需求..." placeholder-class="textarea-placeholder-class" /> -->
			</view>
		</view>
		<view class="order-clause-sbmit">
			<view class="service-clause">
				<u-checkbox-group class="check-box" :activeColor="colors" v-model="checkboxValue">
					<u-checkbox name="agree" shape="square" size="20"  label="我已阅读并同意"></u-checkbox>
				</u-checkbox-group>
				<text class="link" @click="showUserAgreement=true">《陪诊服务协议》</text>
			</view>
			<view class="service-submit">
				<view v-if="agreeClause && projectInfo.price" class="btn btnok" @click="$shaken(createOrder) ">
					确认下单
					<text v-if="projectInfo.price">（支付{{projectInfo.price}}元）</text>
				</view>
				<view v-else class="btn submit-disable">确认下单
					<text v-if="projectInfo.price">（支付{{projectInfo.price}}元）</text>
				</view>
			</view>
		</view>
		<u-datetime-picker
			ref="datetimePicker"
			:show="showTime"
			v-model="value1"
			:closeOnClickOverlay="true"
			mode="date"
			:formatter="formatter"
			@close="closeDatePicker"
			@confirm="dateSure"
			@cancel="closeDatePicker"
			></u-datetime-picker>
		<user-agreement :show.sync="showUserAgreement" :content="agreement"></user-agreement>
		
		<u-popup class="" :show="showHospital" mode="bottom" border-radius="34" :closeable="true" :safe-area-inset-bottom="true"
			:mask-close-able="true" height="800" close-icon-pos="top-left" @close="hospitalClose">
			<view class="hospital-wrap">
				<hospital-list :list="shopList" :colors="colors" @select="hospitalSelect"></hospital-list>
			</view>
		
		</u-popup>
	</view>
</template>

<script>
	import hospitalList from '../components/hospital-list.vue'
	import userAgreement from '../components/user-agreement.vue'
	export default {
		components:{
			hospitalList,
			userAgreement,
		},
		props: {
			colors:{
				type:String
			},
			firstBase:{
				type:Boolean,
				default:false,
			},
			projectInfo: {
				type: Object,
				default: () => {},
			},
			shopInfo: {
				type: Object,
				default: () => {},
			},
			shopList: {
				type: Array,
				default: () => [],
			},
			agreement: {
				type: Object,
				default: () => {},
			},
			price: {
				type: [String, Number],
				default: 0.00
			}
		},
		options: {
			styleIsolation: 'shared'
		},
		data() {
			return {
				noClick:true,
				showHospital:false,
				checkboxValue: [],
				hospitalInfo:{},
				hospital: '',
				time: '',
				person: '',
				showTime: false,
				value1: Number(new Date()),
				dateValue:'',
				showUserAgreement:false,
				isChecked:false,
				form:{
					time:[],
					date:'',
					people:'',
					idcard:'',
					phone:'',
					remark:'',
					address:'',
				}
				
			}
		},
		onReady() {
			// 微信小程序需要用此写法
			this.$refs.datetimePicker.setFormatter(this.formatter)
		},
		computed: {
			agreeClause() {
				return this.checkboxValue.length > 0
			}
		},
		options: {
			styleIsolation: 'shared'
		},
		methods: {
			formatter(type, value) {
				if (type === 'year') {
					return `${value}年`
				}
				if (type === 'month') {
					return `${value}月`
				}
				if (type === 'day') {
					return `${value}日`
				}
				return value
			},
			closeDatePicker(){
				this.showTime = false
			},
			dateSure(e){
				const date = new Date(e.value)
				const year = date.getFullYear()
				const month = (date.getMonth() + 1 < 10 ? '0' + (date.getMonth() + 1) : date.getMonth() + 1)
				const day = (date.getDate() < 10 ? '0' + (date.getDate()) : date.getDate())
				this.dateValue = `${year}年${month}月${day}日`
				this.form.date= `${year}-${month}-${day}`
				// this.itemObj.date = this.dateValue
				this.showTime = false
			},
			clickAgree(){
				
			},
			clickContent() {
				if(!this.hospitalInfo.id){
					this.showHospital=true
					return
				}
				this.$emit('onShowContent')
			},
			createOrder(){
				this.isChecked=true
				if(!this.form.time || !this.form.date || !this.form.people || !this.form.idcard || !this.form.phone){
						uni.showToast({
							title:'请填写必填信息',
							icon:'none'
						})
						return
				}
				
				this.isChecked = false
				this.$emit('createOrder',this.form)
				// console.log(this.form)
			},
			findSelected(item){
				let obj = this.form.time.find(u=>u==item.title)
				if(obj) return true
				return false
			},
			infoItemClick(item){
				let index = this.form.time.indexOf(item.title)
				if(index>=0){
					this.form.time.splice(index)
				}else{
					if(this.projectInfo.selTimesCount==1){//单选操作
						this.form.time=[item.title]
					}else if(this.projectInfo.selTimesCount>0 && this.form.time.length>=this.projectInfo.selTimesCount) return
					else this.form.time.push(item.title)
				}
				
			},
			toPay() {
				console.log('调用支付');
				uni.showToast({
					title: '支付完成，请选择陪诊',
					icon: 'none'
				})
				// this.$emit('next')
			},
			hospitalClose(){
				this.showHospital=false
			},
			hospitalSelect(item)
			{
				this.hospitalInfo=item
				this.showHospital=false
				this.$emit('hospitalSelect',item)
			},
			getLocation() {
				uni.chooseLocation({
					success:(res)=>{
						this.form.address=res.address+res.name
						console.log(res)
					},
				})
				// uni.showLoading({
				// 	title:'请稍等...'
				// })
				// this.$wxsdk.getFuzzyLocationToAddress().then(res => {
					
				// 		console.log(res.address)
				// 	this.form.address=res.address
				// 	uni.hideLoading()
				// }).catch(err=>{
				// 	console.log(err)
				// 	uni.hideLoading()
				// })
			},
		}
	}
</script>
<style>
	.u-checkbox-group{
		padding: 5rpx 20rpx;
	}
	
	.u-checkbox-label--left{
		padding: 20rpx 5rpx;
	}
	
</style>
<style lang="scss" scoped>
	.hospital-wrap{
		padding-top: 80rpx;
	}
	.info-date{
		font-size: 32rpx;
		font-weight: bold;
		letter-spacing:4rpx;
	}
	.info-time{
		font-size: 26rpx;
	}
	
	.required{
		color: #ff0000;
	}
	
	.info-time-box{
		display: flex;
		flex-direction: column;
		align-items: center;
	}
	
	.hospital-order {
		overflow-y: scroll;
		padding-bottom: 260rpx;

		.service-content {
			background-color: #fff;
			padding: 38rpx 20rpx;
			border-radius: 8rpx;
			display: flex;
			align-items: center;
		}

		.order-info {
			margin-top: 20rpx;
			padding: 0 20rpx;
			background-color: #fff;
			z-index: 1;
		}

		.service-demand {
			margin-top: 20rpx;
		}

		.order-clause-sbmit {
			position: fixed;
			bottom: 0;
			left: 0;
			width: 100%;
			background-color: #fff;
			padding: 20rpx;
			z-index: 2;
			border-top: 1rpx solid rgba(0, 0, 0, 0.1);
		}
	}

	.service-content {
		.service-content-icon {
			width: 44rpx;
			height: 44rpx;
			line-height: 44rpx;
			text-align: center;
			margin: 10rpx;
			border-radius: 50%;
			overflow: hidden;
			background-color: #f6d0ff;

			.iconfont {
				font-size: 34rpx;
				color: #9860e9;
			}
		}

		.service-content-title {
			font-size: 30rpx;
			font-weight: bold;
			letter-spacing: 2rpx;
		}

		.service-content-info {
			color: #353535;
			margin-left: auto;
			display: flex;
			align-items: center;

			.iconfont {
				margin-right: 10rpx;
			}
		}
	}
	
	.info-item-text{
		border: 1rpx solid var(--colors2);
		border-radius: 30rpx;
		padding: 5rpx 16rpx;
		color: var(--colors);
		font-size: 26rpx;
		
		min-width: 190rpx;
	}
	
	.info-item-container .info-item-text{
		margin: 10rpx;
		line-height: 1.5;
		width: fit-content;
		&:first-child{
			margin-top: 20rpx;
			
		}
		&:last-child{
			margin-bottom: 20rpx;
		}
	}
	
	.info-selected{
		// background: #ffe4e6;
		background-color: var(--colors-bg);
		
	}
	
	.required-text{
		position: absolute;
		line-height: 1;
		font-size: 16rpx;
		color: #f00;
	}

	.order-info {
		.order-info-item {
			height: auto;
			min-height: 106rpx;
			line-height: 106rpx;
			border-bottom: 2rpx solid #eee;
			display: flex;
			align-items: center;

			&:last-child {
				border-bottom: none;
			}

			.info-item-title {
				width: 160rpx;
				line-height: 1.5;
			}
			
			.info-item-title.hospital{
				width:auto;
			}

			.info-item-container {
				flex: 1;
				text-align: center;
				overflow: hidden;
				// padding-top: 20rpx;

				/deep/ .input-placeholder-class {
					color: #c9c9c9;
					letter-spacing: 2rpx;
					font-size: 28rpx;
				}

				.item-cotent-input {
					text-align: right;
					padding-right: 20rpx;
				}

				.info-item-content {
					max-width: calc(100% - 140rpx);
					text-overflow: ellipsis;
					overflow: hidden;
					white-space: nowrap;
					padding-right: 20rpx;
					text-align: right;
				}

				.no-content {
					color: #c8c7cd;
				}

				.iconfont {
					color: #c8c7cd;
					font-size: 24rpx;
				}
			}
		}
	}

	.service-demand {
		.demand-title {
			font-size: 30rpx;
			letter-spacing: 2rpx;
			padding: 10rpx 0;
		}

		.demand-content {
			background-color: #fff;
			padding: 20rpx 20rpx;
			border-radius: 8rpx;
			font-size: 24rpx;

			// /deep/ .textarea-placeholder-class {
			// 	color: #c9c9c9;
			// 	letter-spacing: 2rpx;
			// }

			// /deep/ textarea,uni-textarea {
			// 	width: 100%;
			// 	height: 100rpx;
			// }
		}
	}

	.order-clause-sbmit {
		.service-clause {
			display: flex;
			align-items: center;
			justify-content: center;
			color: #323232;
			// margin-bottom: 28rpx;
			.check-box{
				padding:24rpx 10rpx
			}
			.link {
				color: var(--colors)
			}
		}

		.service-submit {
			//padding-right: 40rpx;
			height: 90rpx;
			
			.btn{
				height: 90rpx;
				font-size: 32rpx;
				line-height: 90rpx;
				text-align: center;
				
			}
			
			.btnok{
				background-color: var(--colors);
				color: #fff;
			}

			.submit-disable {
				background-color: #f7f7f7;
				color: #9d9d9d;
				
			}
		}
		
	}
	
	.tip-message{
		font-size: 20rpx;
		color: #c8c7cd;
	}
	
	.address{
		display: flex;
		line-height: 1;
		align-items: center;
		.gps{
			line-height: 1;
			color: var(--colors);
			border:1rpx solid var(--colors);
			border-radius: 6rpx;
			color: var(--colors);
			padding: 8rpx 16rpx;
			
		}
		
		/deep/ .u-textarea{
			border:none;
			text-align: left;
		}
		.item-cotent-input{
			flex: 1;
		}
	}
</style>